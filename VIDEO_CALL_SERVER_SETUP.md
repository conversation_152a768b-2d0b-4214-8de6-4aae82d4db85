# Video Call Server Setup Guide

## Overview
This guide explains how to set up and manage the video call signaling server for your Laravel application, particularly when deploying to Hostinger or similar hosting providers.

## Features Added

### 1. Video Call Server Management Page
- **Location**: Settings → Video Call Server (Admin only)
- **Features**:
  - Start/Stop signaling server
  - Real-time server status monitoring
  - Server logs viewing
  - Port status checking
  - Process ID tracking

### 2. Backend Components
- **Controller**: `app/Http/Controllers/VideoCallServerController.php`
- **View**: `resources/views/app/settings/video_call_server.blade.php`
- **Routes**: Added to `routes/web.php` under admin middleware
- **Menu**: Added to Settings menu in navigation

## Setup Instructions

### For Hostinger Deployment

#### 1. Upload Files
Upload your entire Laravel project including the `webrtc-signaling-server` directory to your Hostinger hosting account.

#### 2. Install Node.js Dependencies
```bash
cd webrtc-signaling-server
npm install
```

#### 3. Configure Server Settings
The server management page allows you to:
- Start the signaling server on port 3001
- Monitor server status
- View server logs
- Stop the server when needed

#### 4. Access the Management Page
1. Login to your Laravel admin panel
2. Navigate to **Settings** → **Video Call Server**
3. Click **Start Server** to begin the signaling server
4. Monitor the status and logs

### Server Configuration

#### Default Settings
- **Port**: 3001
- **Protocol**: WebSocket (ws://)
- **Server File**: `webrtc-signaling-server/server.js`
- **Log File**: `storage/logs/video_call_server.log`
- **PID File**: `storage/app/video_call_server.pid`

#### Environment Variables
You can configure the signaling server URL in your Flutter apps:
- **Customer App**: Update `AppConfig.signalingServerUrl`
- **Vendor App**: Uses environment variable `SIGNALING_SERVER_URL`

### Hostinger Specific Considerations

#### 1. Port Restrictions
- Hostinger may restrict certain ports
- Port 3001 should work on most shared hosting
- If blocked, try alternative ports (3002, 3003, etc.)

#### 2. Process Management
- The management page handles process lifecycle
- Automatic PID tracking for proper cleanup
- Cross-platform support (Windows/Linux)

#### 3. Firewall Settings
Ensure your hosting provider allows:
- Outbound connections on port 3001
- WebSocket connections
- Node.js process execution

### Troubleshooting

#### Common Issues

1. **Server Won't Start**
   - Check if Node.js is installed
   - Verify file permissions
   - Check available ports

2. **Port Already in Use**
   - Stop existing processes
   - Use the management page to stop server
   - Check for zombie processes

3. **Connection Refused**
   - Verify firewall settings
   - Check if port is accessible
   - Ensure server is actually running

#### Debugging Steps

1. **Check Server Status**
   ```bash
   # Via management page or manually:
   netstat -tulpn | grep 3001
   ```

2. **View Logs**
   - Use the management page log viewer
   - Or check: `storage/logs/video_call_server.log`

3. **Manual Server Start** (for testing)
   ```bash
   cd webrtc-signaling-server
   node server.js
   ```

### Security Considerations

#### 1. Access Control
- Management page restricted to admin users only
- Server operations logged for audit trail

#### 2. Process Security
- PID file prevents multiple instances
- Proper process cleanup on stop

#### 3. Network Security
- Consider using SSL/TLS for production
- Implement authentication for signaling server

### Production Deployment

#### 1. SSL/HTTPS Setup
For production, configure SSL:
- Use `wss://` instead of `ws://`
- Update signaling server to support HTTPS
- Configure SSL certificates

#### 2. Process Monitoring
Consider using process managers:
- PM2 for Node.js process management
- Supervisor for system-level monitoring
- Auto-restart on crashes

#### 3. Load Balancing
For high traffic:
- Multiple signaling server instances
- Load balancer configuration
- Session affinity for WebSocket connections

### API Endpoints

The management page uses these endpoints:
- `GET /setting/video-call-server` - Management page
- `POST /setting/video-call-server/start` - Start server
- `POST /setting/video-call-server/stop` - Stop server
- `GET /setting/video-call-server/status` - Get status
- `GET /setting/video-call-server/logs` - Get logs

### Integration with Flutter Apps

#### Customer App Configuration
Update `customerapp/lib/config/app_config.dart`:
```dart
static const String signalingServerUrl = 'ws://your-domain.com:3001';
```

#### Vendor App Configuration
Set environment variable or update configuration:
```dart
const signalingUrl = String.fromEnvironment(
  'SIGNALING_SERVER_URL',
  defaultValue: 'ws://your-domain.com:3001',
);
```

## Support

For issues or questions:
1. Check the management page logs
2. Verify server status indicators
3. Test manual server startup
4. Contact hosting provider for port/firewall issues

## Next Steps

After successful setup:
1. Test video calls between customer and vendor apps
2. Monitor server performance and logs
3. Configure SSL for production use
4. Set up automated monitoring and alerts

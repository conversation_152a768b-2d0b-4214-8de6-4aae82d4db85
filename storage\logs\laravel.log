[2025-05-06 17:04:50] production.DEBUG: Error ["[object] (Google\\Cloud\\Core\\Exception\\ServiceException(code: 401): {
  \"error\": {
    \"code\": 401,
    \"message\": \"Request had invalid authentication credentials. Expected OAuth 2 access token, login cookie or other valid authentication credential. See https://developers.google.com/identity/sign-in/web/devconsole-project.\",
    \"errors\": [
      {
        \"message\": \"Invalid Credentials\",
        \"domain\": \"global\",
        \"reason\": \"authError\",
        \"location\": \"Authorization\",
        \"locationType\": \"header\"
      }
    ],
    \"status\": \"UNAUTHENTICATED\"
  }
}
 at /Users/<USER>/Desktop/Dev/web/glover/vendor/google/cloud-core/src/RequestWrapper.php:434)
[stacktrace]
#0 /Users/<USER>/Desktop/Dev/web/glover/vendor/google/cloud-core/src/RequestWrapper.php(230): Google\\Cloud\\Core\\RequestWrapper->convertToGoogleException(Object(GuzzleHttp\\Exception\\ClientException))
#1 /Users/<USER>/Desktop/Dev/web/glover/vendor/google/cloud-core/src/RestTrait.php(102): Google\\Cloud\\Core\\RequestWrapper->send(Object(GuzzleHttp\\Psr7\\Request), Array)
#2 /Users/<USER>/Desktop/Dev/web/glover/vendor/google/cloud-translate/src/V2/Connection/Rest.php(89): Google\\Cloud\\Translate\\V2\\Connection\\Rest->send('translations', 'translate', Array)
#3 /Users/<USER>/Desktop/Dev/web/glover/vendor/google/cloud-translate/src/V2/TranslateClient.php(253): Google\\Cloud\\Translate\\V2\\Connection\\Rest->listTranslations(Array)
#4 /Users/<USER>/Desktop/Dev/web/glover/vendor/google/cloud-translate/src/V2/TranslateClient.php(187): Google\\Cloud\\Translate\\V2\\TranslateClient->translateBatch(Array, Array)
#5 /Users/<USER>/Desktop/Dev/web/glover/app/Console/Commands/AutoTranslator.php(96): Google\\Cloud\\Translate\\V2\\TranslateClient->translate('Item is out of ...', Array)
#6 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Console/Concerns/InteractsWithIO.php(267): App\\Console\\Commands\\AutoTranslator->App\\Console\\Commands\\{closure}('Item is out of ...', Object(Symfony\\Component\\Console\\Helper\\ProgressBar))
#7 /Users/<USER>/Desktop/Dev/web/glover/app/Console/Commands/AutoTranslator.php(93): Illuminate\\Console\\Command->withProgressBar(Array, Object(Closure))
#8 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Console\\Commands\\AutoTranslator->handle()
#9 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 /Users/<USER>/Desktop/Dev/web/glover/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 /Users/<USER>/Desktop/Dev/web/glover/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 /Users/<USER>/Desktop/Dev/web/glover/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\AutoTranslator), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 /Users/<USER>/Desktop/Dev/web/glover/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 /Users/<USER>/Desktop/Dev/web/glover/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}
"] 
[2025-05-06 17:11:21] production.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'glover' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'glover' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) at /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#1 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#2 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#3 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#4 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#5 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#6 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#7 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(355): Illuminate\\Database\\Schema\\Builder->hasTable('settings')
#8 /Users/<USER>/Desktop/Dev/web/glover/app/Console/Commands/MakeUpgrade.php(74): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#9 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Console/Concerns/HasParameters.php(20): App\\Console\\Commands\\MakeUpgrade->getArguments()
#10 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Console/Command.php(120): Illuminate\\Console\\Command->specifyParameters()
#11 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Console/GeneratorCommand.php(127): Illuminate\\Console\\Command->__construct()
#12 [internal function]: Illuminate\\Console\\GeneratorCommand->__construct(Object(Illuminate\\Filesystem\\Filesystem))
#13 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Container/Container.php(952): ReflectionClass->newInstanceArgs(Array)
#14 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('App\\\\Console\\\\Com...')
#15 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Console\\\\Com...', Array, true)
#16 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Console\\\\Com...', Array)
#17 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('App\\\\Console\\\\Com...', Array)
#18 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Console/Application.php(251): Illuminate\\Foundation\\Application->make('App\\\\Console\\\\Com...')
#19 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(349): Illuminate\\Console\\Application->resolve('App\\\\Console\\\\Com...')
#20 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Console/Application.php(130): Illuminate\\Foundation\\Console\\Kernel->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Console\\Application))
#21 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Console/Application.php(77): Illuminate\\Console\\Application->bootstrap()
#22 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(481): Illuminate\\Console\\Application->__construct(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Events\\Dispatcher), '10.48.28')
#23 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Illuminate\\Foundation\\Console\\Kernel->getArtisan()
#24 /Users/<USER>/Desktop/Dev/web/glover/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', '', Array)
#2 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', '', Array)
#3 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func(Object(Closure))
#7 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#8 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#9 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select table_na...', Array)
#10 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#11 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#12 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#13 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#14 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#15 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#16 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#17 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(355): Illuminate\\Database\\Schema\\Builder->hasTable('settings')
#18 /Users/<USER>/Desktop/Dev/web/glover/app/Console/Commands/MakeUpgrade.php(74): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#19 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Console/Concerns/HasParameters.php(20): App\\Console\\Commands\\MakeUpgrade->getArguments()
#20 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Console/Command.php(120): Illuminate\\Console\\Command->specifyParameters()
#21 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Console/GeneratorCommand.php(127): Illuminate\\Console\\Command->__construct()
#22 [internal function]: Illuminate\\Console\\GeneratorCommand->__construct(Object(Illuminate\\Filesystem\\Filesystem))
#23 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Container/Container.php(952): ReflectionClass->newInstanceArgs(Array)
#24 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build('App\\\\Console\\\\Com...')
#25 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Console\\\\Com...', Array, true)
#26 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Console\\\\Com...', Array)
#27 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('App\\\\Console\\\\Com...', Array)
#28 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Console/Application.php(251): Illuminate\\Foundation\\Application->make('App\\\\Console\\\\Com...')
#29 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(349): Illuminate\\Console\\Application->resolve('App\\\\Console\\\\Com...')
#30 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Console/Application.php(130): Illuminate\\Foundation\\Console\\Kernel->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Console\\Application))
#31 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Console/Application.php(77): Illuminate\\Console\\Application->bootstrap()
#32 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(481): Illuminate\\Console\\Application->__construct(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Events\\Dispatcher), '10.48.28')
#33 /Users/<USER>/Desktop/Dev/web/glover/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Illuminate\\Foundation\\Console\\Kernel->getArtisan()
#34 /Users/<USER>/Desktop/Dev/web/glover/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 {main}
"} 
[2025-06-09 15:41:47] production.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 {main}
"} 
[2025-06-09 15:41:48] production.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 {main}
"} 
[2025-06-09 15:41:54] production.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 {main}
"} 
[2025-06-09 15:41:54] production.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 {main}
"} 
[2025-06-09 15:41:54] production.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 {main}
"} 
[2025-06-09 15:41:55] production.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 {main}
"} 
[2025-06-09 15:42:08] production.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 {main}
"} 
[2025-06-09 15:42:27] production.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 {main}
"} 
[2025-06-09 15:42:32] production.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 {main}
"} 
[2025-06-09 15:43:44] production.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 {main}
"} 
[2025-06-09 15:43:56] production.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 {main}
"} 
[2025-06-09 15:45:51] production.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 {main}
"} 
[2025-06-09 15:45:58] production.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 {main}
"} 
[2025-06-09 15:46:02] production.ERROR: SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] Connection refused (Connection: mysql, SQL: select * from `settings`) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#22 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#30 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] Connection refused at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/anlutro/l4-settings/src/helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Traits/VerificationHelperTrait.php(15): setting()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/ambrosethebuild/epcv/src/Providers/EnvatoPurchaseCodeVerifierServiceProvider.php(48): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->getVerificationCode()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Ambrosethebuild\\EnvatoPurchaseCodeVerifier\\Providers\\EnvatoPurchaseCodeVerifierServiceProvider->boot()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1059): Illuminate\\Container\\Container->call()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1040): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1039): array_walk()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(286): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 {main}
"} 
[2025-06-09 19:15:55] production.ERROR: Command "storage:link
php" is not defined.

Did you mean one of these?
    storage:link
    storage:unlink {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"storage:link
php\" is not defined.

Did you mean one of these?
    storage:link
    storage:unlink at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/symfony/console/Application.php:737)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/symfony/console/Application.php(266): Symfony\\Component\\Console\\Application->find()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 {main}
"} 
[2025-06-10 21:33:20] production.DEBUG: Error restart service: Reverb ["[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): The command \"reverb:restart\" does not exist. at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Console/Application.php:159)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(400): Illuminate\\Console\\Application->call()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(355): Illuminate\\Foundation\\Console\\Kernel->call()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/app/Http/Livewire/WebsocketSettingsLivewire.php(38): Illuminate\\Support\\Facades\\Facade::__callStatic()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Http\\Livewire\\WebsocketSettingsLivewire->regenerateKeys()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/livewire/livewire/src/ComponentConcerns/HandlesActions.php(149): Illuminate\\Container\\BoundMethod::call()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/livewire/livewire/src/HydrationMiddleware/PerformActionCalls.php(36): Livewire\\Component->callMethod()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/livewire/livewire/src/LifecycleManager.php(89): Livewire\\HydrationMiddleware\\PerformActionCalls::hydrate()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/livewire/livewire/src/Connection/ConnectionHandler.php(13): Livewire\\LifecycleManager->hydrate()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/livewire/livewire/src/Controllers/HttpConnectionHandler.php(19): Livewire\\Connection\\ConnectionHandler->handle()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): Livewire\\Controllers\\HttpConnectionHandler->__invoke()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/app/Http/Middleware/UserLang.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\UserLang->handle()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#41 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/livewire/livewire/src/DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\DisableBrowserCache->handle()
#43 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#45 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#49 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#53 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#57 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#59 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#60 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#61 {main}
"] 
[2025-06-10 21:39:41] production.DEBUG: error saving the delivery address ["[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`u388004462_mart`.`delivery_addresses`, CONSTRAINT `delivery_addresses_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE) (Connection: mysql, SQL: insert into `delivery_addresses` (`name`, `description`, `address`, `city`, `state`, `country`, `latitude`, `longitude`, `is_default`, `updated_at`, `created_at`) values (home, home, F9MX+FJ5, Umm Al Amad, Qatar, Umm Al Amad, Umm Salal Municipality, Qatar, 25.4836451, 51.3991078, 1, 2025-06-10 21:39:41, 2025-06-10 21:39:41)) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(34): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/app/Services/DeliveryAddressService.php(24): Illuminate\\Database\\Eloquent\\Model->save()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/app/Http/Controllers/API/DeliveryAddressController.php(104): App\\Services\\DeliveryAddressService::saveOrUpdate()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): App\\Http\\Controllers\\API\\DeliveryAddressController->store()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/app/Http/Middleware/UserActiveCheck.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\UserActiveCheck->handle()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/app/Http/Middleware/UserLang.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\UserLang->handle()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(24): Illuminate\\Pipeline\\Pipeline->then()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#33 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/livewire/livewire/src/DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\DisableBrowserCache->handle()
#42 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#44 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#46 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#48 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#49 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#51 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#52 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#54 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#56 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#58 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#60 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#61 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#62 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`u388004462_mart`.`delivery_addresses`, CONSTRAINT `delivery_addresses_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:45)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(45): PDOStatement->execute()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(34): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/app/Services/DeliveryAddressService.php(24): Illuminate\\Database\\Eloquent\\Model->save()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/app/Http/Controllers/API/DeliveryAddressController.php(104): App\\Services\\DeliveryAddressService::saveOrUpdate()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): App\\Http\\Controllers\\API\\DeliveryAddressController->store()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/app/Http/Middleware/UserActiveCheck.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\UserActiveCheck->handle()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/app/Http/Middleware/UserLang.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\UserLang->handle()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(24): Illuminate\\Pipeline\\Pipeline->then()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#41 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#42 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/livewire/livewire/src/DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\DisableBrowserCache->handle()
#44 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#46 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#48 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#51 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#53 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#54 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#56 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#58 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#60 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#62 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#63 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#64 {main}
"] 
[2025-06-10 23:33:35] production.DEBUG: chmod Process output ["Done"] 
[2025-06-10 23:33:35] production.DEBUG: Unzip Process output ["Done"] 
[2025-06-10 23:33:35] production.DEBUG: Done Extract ["Yes"] 
[2025-06-10 23:33:35] production.DEBUG: extensionComponetLocation ["DriverLiveTracking"] 
[2025-06-10 23:34:09] production.DEBUG: chmod Process output ["Done"] 
[2025-06-10 23:34:09] production.DEBUG: Unzip Process output ["Done"] 
[2025-06-10 23:34:09] production.DEBUG: Done Extract ["Yes"] 
[2025-06-10 23:34:09] production.DEBUG: extensionComponetLocation ["Emailer"] 
[2025-06-10 23:45:38] production.DEBUG: error saving the delivery address ["[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`u388004462_mart`.`delivery_addresses`, CONSTRAINT `delivery_addresses_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE) (Connection: mysql, SQL: insert into `delivery_addresses` (`name`, `description`, `address`, `city`, `state`, `country`, `latitude`, `longitude`, `is_default`, `updated_at`, `created_at`) values (home, ?, 7GPJ+5CF, Doha, Qatar, Doha, Doha Municipality, Qatar, 25.2854375, 51.5310469, 1, 2025-06-10 23:45:38, 2025-06-10 23:45:38)) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(34): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/app/Services/DeliveryAddressService.php(24): Illuminate\\Database\\Eloquent\\Model->save()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/app/Http/Controllers/API/DeliveryAddressController.php(104): App\\Services\\DeliveryAddressService::saveOrUpdate()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): App\\Http\\Controllers\\API\\DeliveryAddressController->store()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/app/Http/Middleware/UserActiveCheck.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\UserActiveCheck->handle()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/app/Http/Middleware/UserLang.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\UserLang->handle()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(24): Illuminate\\Pipeline\\Pipeline->then()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#33 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/livewire/livewire/src/DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\DisableBrowserCache->handle()
#42 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#44 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#46 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#48 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#49 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#51 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#52 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#54 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#56 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#58 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#60 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#61 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#62 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`u388004462_mart`.`delivery_addresses`, CONSTRAINT `delivery_addresses_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE) at /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php:45)
[stacktrace]
#0 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(45): PDOStatement->execute()
#1 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}()
#2 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#3 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/MySqlConnection.php(34): Illuminate\\Database\\Connection->run()
#4 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert()
#5 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId()
#6 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId()
#7 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call()
#8 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#9 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert()
#10 /home/<USER>/domains/mart.wafipos.com/public_html/app/Services/DeliveryAddressService.php(24): Illuminate\\Database\\Eloquent\\Model->save()
#11 /home/<USER>/domains/mart.wafipos.com/public_html/app/Http/Controllers/API/DeliveryAddressController.php(104): App\\Services\\DeliveryAddressService::saveOrUpdate()
#12 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Controller.php(54): App\\Http\\Controllers\\API\\DeliveryAddressController->store()
#13 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#14 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#15 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#16 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#17 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#18 /home/<USER>/domains/mart.wafipos.com/public_html/app/Http/Middleware/UserActiveCheck.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\UserActiveCheck->handle()
#20 /home/<USER>/domains/mart.wafipos.com/public_html/app/Http/Middleware/UserLang.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): App\\Http\\Middleware\\UserLang->handle()
#22 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#24 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#26 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#27 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#28 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#30 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#32 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/sanctum/src/Http/Middleware/EnsureFrontendRequestsAreStateful.php(24): Illuminate\\Pipeline\\Pipeline->then()
#34 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#35 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#37 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#38 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#39 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#40 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#41 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#42 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/livewire/livewire/src/DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\DisableBrowserCache->handle()
#44 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle()
#46 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#48 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#51 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#53 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#54 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#56 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#58 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#60 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#62 /home/<USER>/domains/mart.wafipos.com/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#63 /home/<USER>/domains/mart.wafipos.com/public_html/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#64 {main}
"] 
[2025-06-11 10:51:41] production.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select * from `settings`) {"view":{"view":"C:\\xampp\\htdocs\\resources\\views\\livewire\\website\\welcome.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-787901820 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#3372</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-787901820\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","view":"<pre class=sf-dump id=sf-dump-1521351227 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"15 characters\">layouts.website</span>\"
</pre><script>Sfdump(\"sf-dump-1521351227\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","params":"<pre class=sf-dump id=sf-dump-1918510610 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>attributes</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\ComponentAttributeBag
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ComponentAttributeBag</span></span> {<a class=sf-dump-ref>#3345</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
  </samp>}
</samp>]
</pre><script>Sfdump(\"sf-dump-1918510610\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","slotOrSection":"<pre class=sf-dump id=sf-dump-1436488659 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"4 characters\">slot</span>\"
</pre><script>Sfdump(\"sf-dump-1436488659\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","manager":"<pre class=sf-dump id=sf-dump-1568026681 data-indent-pad=\"  \"><span class=sf-dump-note>Livewire\\LifecycleManager</span> {<a class=sf-dump-ref>#3348</a><samp data-depth=1 class=sf-dump-expanded>
  +<span class=sf-dump-public title=\"Public property\">request</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Request
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Request</span></span> {<a class=sf-dump-ref href=#sf-dump-1568026681-ref23357 title=\"2 occurrences\">#3357</a><samp data-depth=2 id=sf-dump-1568026681-ref23357 class=sf-dump-compact>
    +<span class=sf-dump-public title=\"Public property\">fingerprint</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">YWuIHcngX2HtqrsPnhlU</span>\"
      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">website.welcome-livewire</span>\"
      \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"
      \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str>/</span>\"
      \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"
    </samp>]
    +<span class=sf-dump-public title=\"Public property\">updates</span>: []
    +<span class=sf-dump-public title=\"Public property\">memo</span>: []
  </samp>}
  +<span class=sf-dump-public title=\"Public property\">instance</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Http\\Livewire\\Website\\WelcomeLivewire
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Livewire\\Website</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">WelcomeLivewire</span></span> {<a class=sf-dump-ref href=#sf-dump-1568026681-ref23340 title=\"3 occurrences\">#3340</a><samp data-depth=2 id=sf-dump-1568026681-ref23340 class=sf-dump-compact>
    +<span class=sf-dump-public title=\"Public property\">id</span>: \"<span class=sf-dump-str title=\"20 characters\">YWuIHcngX2HtqrsPnhlU</span>\"
    #<span class=sf-dump-protected title=\"Protected property\">queryString</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">computedPropertyCache</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">shouldSkipRender</span>: <span class=sf-dump-const>null</span>
    #<span class=sf-dump-protected title=\"Protected property\">preRenderedView</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\View
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">View</span></span> {<a class=sf-dump-ref>#3358</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">factory</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref>#450</a> &#8230;23}
      #<span class=sf-dump-protected title=\"Protected property\">engine</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\LivewireViewCompilerEngine
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LivewireViewCompilerEngine</span></span> {<a class=sf-dump-ref>#3336</a> &#8230;5}
      #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"24 characters\">livewire.website.welcome</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">data</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"66 characters\">C:\\xampp\\htdocs\\resources\\views/livewire/website/welcome.blade.php</span>\"
    </samp>}
    #<span class=sf-dump-protected title=\"Protected property\">forStack</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">errorBag</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\MessageBag
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">MessageBag</span></span> {<a class=sf-dump-ref>#3353</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">messages</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">format</span>: \"<span class=sf-dump-str title=\"8 characters\">:message</span>\"
    </samp>}
    #<span class=sf-dump-protected title=\"Protected property\">withValidatorCallback</span>: <span class=sf-dump-const>null</span>
    #<span class=sf-dump-protected title=\"Protected property\">eventQueue</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">dispatchQueue</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">listeners</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>refreshView</span>\" => \"<span class=sf-dump-str title=\"8 characters\">$refresh</span>\"
      \"<span class=sf-dump-key>openNewTab</span>\" => \"<span class=sf-dump-str title=\"10 characters\">openNewTab</span>\"
    </samp>]
    +<span class=sf-dump-public title=\"Public property\">redirectTo</span>: <span class=sf-dump-const>null</span>
    #<span class=sf-dump-protected title=\"Protected property\">renderedChildren</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">previouslyRenderedChildren</span>: []
  </samp>}
  +<span class=sf-dump-public title=\"Public property\">response</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Response
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Response</span></span> {<a class=sf-dump-ref>#3342</a><samp data-depth=2 class=sf-dump-compact>
    +<span class=sf-dump-public title=\"Public property\">request</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Request
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Request</span></span> {<a class=sf-dump-ref href=#sf-dump-1568026681-ref23357 title=\"2 occurrences\">#3357</a>}
    +<span class=sf-dump-public title=\"Public property\">fingerprint</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">YWuIHcngX2HtqrsPnhlU</span>\"
      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">website.welcome-livewire</span>\"
      \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"
      \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str>/</span>\"
      \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"
    </samp>]
    +<span class=sf-dump-public title=\"Public property\">effects</span>: []
    +<span class=sf-dump-public title=\"Public property\">memo</span>: []
  </samp>}
</samp>}
</pre><script>Sfdump(\"sf-dump-1568026681\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select * from `settings`) at C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get('websiteName', 'Mart')
#13 C:\\xampp\\htdocs\\resources\\views\\livewire\\website\\welcome.blade.php(7): setting('websiteName', 'Mart')
#14 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(83): include('C:\\\\xampp\\\\htdocs...')
#15 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Website\\WelcomeLivewire->Livewire\\ComponentConcerns\\{closure}()
#16 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#17 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#18 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#19 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#20 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#21 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#22 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Website\\WelcomeLivewire), Object(Livewire\\Response))
#23 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\Macros\\livewire-view-component.blade.php(3): Livewire\\LifecycleManager->initialDehydrate()
#24 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#25 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#26 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#27 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#28 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#29 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#30 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#31 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#32 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#33 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#34 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#35 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#36 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#37 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#38 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\app\\Http\\Middleware\\UserLang.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#51 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#60 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#83 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#84 C:\\xampp\\htdocs\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#85 {main}

[previous exception] [object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select * from `settings`) at C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get('websiteName', 'Mart')
#13 C:\\xampp\\htdocs\\storage\\framework\\views\\94cde873b7d45c1116e4f59ae5df78e6.php(7): setting('websiteName', 'Mart')
#14 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(83): include('C:\\\\xampp\\\\htdocs...')
#15 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Website\\WelcomeLivewire->Livewire\\ComponentConcerns\\{closure}()
#16 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#17 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#18 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#19 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#20 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#21 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#22 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Website\\WelcomeLivewire), Object(Livewire\\Response))
#23 C:\\xampp\\htdocs\\storage\\framework\\views\\db225c73972485aeb1546477f6fd2f64.php(3): Livewire\\LifecycleManager->initialDehydrate()
#24 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#25 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#26 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#27 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#28 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#29 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#30 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#31 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#32 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#33 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#34 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#35 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#36 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#37 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#38 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\app\\Http\\Middleware\\UserLang.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#51 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#60 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#83 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#84 C:\\xampp\\htdocs\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#85 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=loca...', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=loca...', 'root', '', Array)
#2 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=loca...', 'root', '', Array)
#3 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=loca...', Array, Array)
#4 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#7 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#11 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#13 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#14 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#15 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#16 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#19 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get('websiteName', 'Mart')
#24 C:\\xampp\\htdocs\\storage\\framework\\views\\94cde873b7d45c1116e4f59ae5df78e6.php(7): setting('websiteName', 'Mart')
#25 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(83): include('C:\\\\xampp\\\\htdocs...')
#26 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Website\\WelcomeLivewire->Livewire\\ComponentConcerns\\{closure}()
#27 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#28 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#29 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#30 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#31 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#32 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#33 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Website\\WelcomeLivewire), Object(Livewire\\Response))
#34 C:\\xampp\\htdocs\\storage\\framework\\views\\db225c73972485aeb1546477f6fd2f64.php(3): Livewire\\LifecycleManager->initialDehydrate()
#35 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#36 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#37 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#38 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#39 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#40 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#41 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#42 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#43 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#44 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#45 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#46 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#47 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#48 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#49 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\app\\Http\\Middleware\\UserLang.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#62 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#71 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#72 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#73 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\xampp\\htdocs\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#89 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#93 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#94 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#95 C:\\xampp\\htdocs\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#96 {main}
"} 
[2025-06-11 10:51:49] production.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select * from `settings`) {"view":{"view":"C:\\xampp\\htdocs\\resources\\views\\livewire\\website\\welcome.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-848855115 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#3372</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-848855115\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","view":"<pre class=sf-dump id=sf-dump-896141440 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"15 characters\">layouts.website</span>\"
</pre><script>Sfdump(\"sf-dump-896141440\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","params":"<pre class=sf-dump id=sf-dump-1859004120 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>attributes</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\ComponentAttributeBag
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ComponentAttributeBag</span></span> {<a class=sf-dump-ref>#3345</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: []
  </samp>}
</samp>]
</pre><script>Sfdump(\"sf-dump-1859004120\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","slotOrSection":"<pre class=sf-dump id=sf-dump-2085811203 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"4 characters\">slot</span>\"
</pre><script>Sfdump(\"sf-dump-2085811203\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","manager":"<pre class=sf-dump id=sf-dump-1817728209 data-indent-pad=\"  \"><span class=sf-dump-note>Livewire\\LifecycleManager</span> {<a class=sf-dump-ref>#3348</a><samp data-depth=1 class=sf-dump-expanded>
  +<span class=sf-dump-public title=\"Public property\">request</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Request
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Request</span></span> {<a class=sf-dump-ref href=#sf-dump-1817728209-ref23357 title=\"2 occurrences\">#3357</a><samp data-depth=2 id=sf-dump-1817728209-ref23357 class=sf-dump-compact>
    +<span class=sf-dump-public title=\"Public property\">fingerprint</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">3cUOgZXNUm75ltMUFoGT</span>\"
      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">website.welcome-livewire</span>\"
      \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"
      \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str>/</span>\"
      \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"
    </samp>]
    +<span class=sf-dump-public title=\"Public property\">updates</span>: []
    +<span class=sf-dump-public title=\"Public property\">memo</span>: []
  </samp>}
  +<span class=sf-dump-public title=\"Public property\">instance</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Http\\Livewire\\Website\\WelcomeLivewire
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Livewire\\Website</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">WelcomeLivewire</span></span> {<a class=sf-dump-ref href=#sf-dump-1817728209-ref23340 title=\"3 occurrences\">#3340</a><samp data-depth=2 id=sf-dump-1817728209-ref23340 class=sf-dump-compact>
    +<span class=sf-dump-public title=\"Public property\">id</span>: \"<span class=sf-dump-str title=\"20 characters\">3cUOgZXNUm75ltMUFoGT</span>\"
    #<span class=sf-dump-protected title=\"Protected property\">queryString</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">computedPropertyCache</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">shouldSkipRender</span>: <span class=sf-dump-const>null</span>
    #<span class=sf-dump-protected title=\"Protected property\">preRenderedView</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\View
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">View</span></span> {<a class=sf-dump-ref>#3358</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">factory</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref>#450</a> &#8230;23}
      #<span class=sf-dump-protected title=\"Protected property\">engine</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\LivewireViewCompilerEngine
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LivewireViewCompilerEngine</span></span> {<a class=sf-dump-ref>#3336</a> &#8230;5}
      #<span class=sf-dump-protected title=\"Protected property\">view</span>: \"<span class=sf-dump-str title=\"24 characters\">livewire.website.welcome</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">data</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"66 characters\">C:\\xampp\\htdocs\\resources\\views/livewire/website/welcome.blade.php</span>\"
    </samp>}
    #<span class=sf-dump-protected title=\"Protected property\">forStack</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">errorBag</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\MessageBag
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">MessageBag</span></span> {<a class=sf-dump-ref>#3353</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">messages</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">format</span>: \"<span class=sf-dump-str title=\"8 characters\">:message</span>\"
    </samp>}
    #<span class=sf-dump-protected title=\"Protected property\">withValidatorCallback</span>: <span class=sf-dump-const>null</span>
    #<span class=sf-dump-protected title=\"Protected property\">eventQueue</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">dispatchQueue</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">listeners</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>refreshView</span>\" => \"<span class=sf-dump-str title=\"8 characters\">$refresh</span>\"
      \"<span class=sf-dump-key>openNewTab</span>\" => \"<span class=sf-dump-str title=\"10 characters\">openNewTab</span>\"
    </samp>]
    +<span class=sf-dump-public title=\"Public property\">redirectTo</span>: <span class=sf-dump-const>null</span>
    #<span class=sf-dump-protected title=\"Protected property\">renderedChildren</span>: []
    #<span class=sf-dump-protected title=\"Protected property\">previouslyRenderedChildren</span>: []
  </samp>}
  +<span class=sf-dump-public title=\"Public property\">response</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Response
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Response</span></span> {<a class=sf-dump-ref>#3343</a><samp data-depth=2 class=sf-dump-compact>
    +<span class=sf-dump-public title=\"Public property\">request</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Request
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Request</span></span> {<a class=sf-dump-ref href=#sf-dump-1817728209-ref23357 title=\"2 occurrences\">#3357</a>}
    +<span class=sf-dump-public title=\"Public property\">fingerprint</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>
      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">3cUOgZXNUm75ltMUFoGT</span>\"
      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">website.welcome-livewire</span>\"
      \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"
      \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str>/</span>\"
      \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"
    </samp>]
    +<span class=sf-dump-public title=\"Public property\">effects</span>: []
    +<span class=sf-dump-public title=\"Public property\">memo</span>: []
  </samp>}
</samp>}
</pre><script>Sfdump(\"sf-dump-1817728209\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select * from `settings`) at C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get('websiteName', 'Mart')
#13 C:\\xampp\\htdocs\\resources\\views\\livewire\\website\\welcome.blade.php(7): setting('websiteName', 'Mart')
#14 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(83): include('C:\\\\xampp\\\\htdocs...')
#15 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Website\\WelcomeLivewire->Livewire\\ComponentConcerns\\{closure}()
#16 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#17 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#18 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#19 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#20 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#21 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#22 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Website\\WelcomeLivewire), Object(Livewire\\Response))
#23 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\Macros\\livewire-view-component.blade.php(3): Livewire\\LifecycleManager->initialDehydrate()
#24 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#25 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#26 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#27 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#28 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#29 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#30 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#31 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#32 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#33 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#34 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#35 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#36 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#37 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#38 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\app\\Http\\Middleware\\UserLang.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#51 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#60 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#83 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#84 C:\\xampp\\htdocs\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#85 {main}

[previous exception] [object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select * from `settings`) at C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#10 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#11 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#12 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get('websiteName', 'Mart')
#13 C:\\xampp\\htdocs\\storage\\framework\\views\\94cde873b7d45c1116e4f59ae5df78e6.php(7): setting('websiteName', 'Mart')
#14 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(83): include('C:\\\\xampp\\\\htdocs...')
#15 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Website\\WelcomeLivewire->Livewire\\ComponentConcerns\\{closure}()
#16 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#17 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#18 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#19 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#20 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#21 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#22 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Website\\WelcomeLivewire), Object(Livewire\\Response))
#23 C:\\xampp\\htdocs\\storage\\framework\\views\\db225c73972485aeb1546477f6fd2f64.php(3): Livewire\\LifecycleManager->initialDehydrate()
#24 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#25 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#26 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#27 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#28 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#29 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#30 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#31 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#32 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#33 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#34 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#35 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#36 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#37 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#38 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\app\\Http\\Middleware\\UserLang.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#51 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#60 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#83 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#84 C:\\xampp\\htdocs\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#85 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:65)
[stacktrace]
#0 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(65): PDO->__construct('mysql:host=loca...', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=loca...', 'root', '', Array)
#2 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=loca...', 'root', '', Array)
#3 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=loca...', Array, Array)
#4 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#7 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1375): Illuminate\\Database\\Connection->getPdo()
#8 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(528): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#11 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#13 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#14 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#15 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#16 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#17 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#19 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\DatabaseSettingStore.php(228): Illuminate\\Database\\Query\\Builder->get()
#20 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(252): anlutro\\LaravelSettings\\DatabaseSettingStore->read()
#21 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(232): anlutro\\LaravelSettings\\SettingStore->readData()
#22 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\SettingStore.php(123): anlutro\\LaravelSettings\\SettingStore->load()
#23 C:\\xampp\\htdocs\\vendor\\anlutro\\l4-settings\\src\\helpers.php(11): anlutro\\LaravelSettings\\SettingStore->get('websiteName', 'Mart')
#24 C:\\xampp\\htdocs\\storage\\framework\\views\\94cde873b7d45c1116e4f59ae5df78e6.php(7): setting('websiteName', 'Mart')
#25 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(83): include('C:\\\\xampp\\\\htdocs...')
#26 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(84): App\\Http\\Livewire\\Website\\WelcomeLivewire->Livewire\\ComponentConcerns\\{closure}()
#27 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(59): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#28 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#29 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#30 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#31 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\Component.php(235): Illuminate\\View\\View->render()
#32 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php(14): Livewire\\Component->output()
#33 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php(141): Livewire\\HydrationMiddleware\\RenderView::dehydrate(Object(App\\Http\\Livewire\\Website\\WelcomeLivewire), Object(Livewire\\Response))
#34 C:\\xampp\\htdocs\\storage\\framework\\views\\db225c73972485aeb1546477f6fd2f64.php(3): Livewire\\LifecycleManager->initialDehydrate()
#35 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\xampp\\\\htdocs...')
#36 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#37 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\xampp\\\\htdocs...', Array)
#38 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#39 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\LivewireViewCompilerEngine->evaluatePath('C:\\\\xampp\\\\htdocs...', Array)
#40 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#41 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\LivewireViewCompilerEngine->get('C:\\\\xampp\\\\htdocs...', Array)
#42 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#43 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#44 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#45 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#46 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#47 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#48 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#49 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\app\\Http\\Middleware\\UserLang.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UserLang->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#62 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#71 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#72 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#73 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 C:\\xampp\\htdocs\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#89 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#93 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#94 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#95 C:\\xampp\\htdocs\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#96 {main}
"} 
[2025-06-12 09:41:55] local.ERROR: Class "ZipArchive" not found {"exception":"[object] (Error(code: 0): Class \"ZipArchive\" not found at C:\\xampp\\htdocs\\vendor\\spatie\\laravel-backup\\config\\backup.php:133)
[stacktrace]
#0 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#1 C:\\xampp\\htdocs\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(54): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('C:\\\\xampp\\\\htdocs...', 'backup')
#2 C:\\xampp\\htdocs\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(32): Spatie\\LaravelPackageTools\\PackageServiceProvider->registerConfigs()
#3 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(817): Spatie\\LaravelPackageTools\\PackageServiceProvider->register()
#4 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Spatie\\Backup\\BackupServiceProvider))
#5 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(794): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 C:\\xampp\\htdocs\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-06-12 09:42:05] local.ERROR: Class "ZipArchive" not found {"exception":"[object] (Error(code: 0): Class \"ZipArchive\" not found at C:\\xampp\\htdocs\\vendor\\spatie\\laravel-backup\\config\\backup.php:133)
[stacktrace]
#0 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#1 C:\\xampp\\htdocs\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(54): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('C:\\\\xampp\\\\htdocs...', 'backup')
#2 C:\\xampp\\htdocs\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(32): Spatie\\LaravelPackageTools\\PackageServiceProvider->registerConfigs()
#3 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(817): Spatie\\LaravelPackageTools\\PackageServiceProvider->register()
#4 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Spatie\\Backup\\BackupServiceProvider))
#5 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(794): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(447): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 C:\\xampp\\htdocs\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 

@extends('layouts.app')

@section('title', 'Video Call Server Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-video"></i>
                        Video Call Server Management
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Server Status Card -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-left-primary">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                Server Status
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="server-status">
                                                @if($status['running'])
                                                    <span class="badge badge-success">Running</span>
                                                @else
                                                    <span class="badge badge-danger">Stopped</span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-server fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-left-info">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                Port Status
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="port-status">
                                                Port {{ $status['port'] }}:
                                                @if($status['port_open'])
                                                    <span class="badge badge-success">Open</span>
                                                @else
                                                    <span class="badge badge-warning">Closed</span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-network-wired fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Server Details -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="m-0 font-weight-bold text-primary">Server Details</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <strong>Process ID:</strong>
                                            <span id="server-pid">{{ $status['pid'] ?? 'N/A' }}</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>Port:</strong>
                                            <span>{{ $status['port'] }}</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>Server Path:</strong>
                                            <span>{{ $status['server_path'] }}</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>Last Checked:</strong>
                                            <span id="last-checked">{{ $status['last_checked'] }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Control Buttons -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-success" id="start-server" 
                                        @if($status['running']) disabled @endif>
                                    <i class="fas fa-play"></i> Start Server
                                </button>
                                <button type="button" class="btn btn-danger" id="stop-server"
                                        @if(!$status['running']) disabled @endif>
                                    <i class="fas fa-stop"></i> Stop Server
                                </button>
                                <button type="button" class="btn btn-info" id="refresh-status">
                                    <i class="fas fa-sync-alt"></i> Refresh Status
                                </button>
                                <button type="button" class="btn btn-secondary" id="refresh-logs">
                                    <i class="fas fa-file-alt"></i> Refresh Logs
                                </button>
                                <button type="button" class="btn btn-warning" id="test-ports">
                                    <i class="fas fa-search"></i> Test Ports
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Port Test Results -->
                    <div class="row mb-4" id="port-test-results" style="display: none;">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="m-0 font-weight-bold text-primary">Port Availability Test</h6>
                                </div>
                                <div class="card-body">
                                    <div id="port-test-content">
                                        <!-- Port test results will be displayed here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Server Logs -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="m-0 font-weight-bold text-primary">Server Logs</h6>
                                </div>
                                <div class="card-body">
                                    <pre id="server-logs" style="height: 400px; overflow-y: auto; background-color: #f8f9fa; padding: 15px; border-radius: 5px;">{{ $logs }}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="mt-2">Processing...</p>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Start Server
    $('#start-server').click(function() {
        showLoading();
        $.post('{{ route("video-call-server.start") }}')
            .done(function(response) {
                hideLoading();
                if (response.success) {
                    showAlert('success', response.message);
                    refreshStatus();
                } else {
                    showAlert('error', response.message);
                }
            })
            .fail(function() {
                hideLoading();
                showAlert('error', 'Failed to communicate with server');
            });
    });

    // Stop Server
    $('#stop-server').click(function() {
        if (confirm('Are you sure you want to stop the video call server?')) {
            showLoading();
            $.post('{{ route("video-call-server.stop") }}')
                .done(function(response) {
                    hideLoading();
                    if (response.success) {
                        showAlert('success', response.message);
                        refreshStatus();
                    } else {
                        showAlert('error', response.message);
                    }
                })
                .fail(function() {
                    hideLoading();
                    showAlert('error', 'Failed to communicate with server');
                });
        }
    });

    // Refresh Status
    $('#refresh-status').click(function() {
        refreshStatus();
    });

    // Refresh Logs
    $('#refresh-logs').click(function() {
        refreshLogs();
    });

    // Auto refresh every 30 seconds
    setInterval(function() {
        refreshStatus();
    }, 30000);

    function refreshStatus() {
        $.get('{{ route("video-call-server.status") }}')
            .done(function(status) {
                updateStatusDisplay(status);
            });
    }

    function refreshLogs() {
        $.get('{{ route("video-call-server.logs") }}')
            .done(function(response) {
                $('#server-logs').text(response.logs);
                // Scroll to bottom
                $('#server-logs').scrollTop($('#server-logs')[0].scrollHeight);
            });
    }

    function testPorts() {
        $('#test-ports').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Testing...');

        $.get('{{ route("video-call-server.test-ports") }}')
            .done(function(response) {
                displayPortTestResults(response);
                $('#port-test-results').show();
            })
            .fail(function() {
                alert('Failed to test ports');
            })
            .always(function() {
                $('#test-ports').prop('disabled', false).html('<i class="fas fa-search"></i> Test Ports');
            });
    }

    function displayPortTestResults(data) {
        let html = '<div class="row">';

        // Recommended port
        html += '<div class="col-12 mb-3">';
        html += '<div class="alert alert-info">';
        html += '<strong>Recommended Port for Hostinger:</strong> ' + data.recommended_port;
        html += '<br><small>This port is most likely to work on shared hosting.</small>';
        html += '</div>';
        html += '</div>';

        // Available ports
        html += '<div class="col-md-6">';
        html += '<h6>Available Ports:</h6>';
        if (data.available_ports.length > 0) {
            html += '<div class="list-group">';
            data.available_ports.forEach(function(port) {
                html += '<div class="list-group-item list-group-item-success">';
                html += '<i class="fas fa-check-circle text-success"></i> Port ' + port + ' - Available';
                html += '</div>';
            });
            html += '</div>';
        } else {
            html += '<div class="alert alert-warning">No available ports found in common range.</div>';
        }
        html += '</div>';

        // Test results
        html += '<div class="col-md-6">';
        html += '<h6>Port Test Results:</h6>';
        html += '<div class="list-group">';
        data.test_results.forEach(function(result) {
            let statusClass = result.available ? 'success' : 'danger';
            let icon = result.available ? 'check-circle' : 'times-circle';
            html += '<div class="list-group-item list-group-item-' + statusClass + '">';
            html += '<i class="fas fa-' + icon + '"></i> Port ' + result.port + ' - ' + result.status;
            html += '</div>';
        });
        html += '</div>';
        html += '</div>';

        html += '</div>';

        $('#port-test-content').html(html);
    }

    function updateStatusDisplay(status) {
        // Update status badge
        if (status.running) {
            $('#server-status').html('<span class="badge badge-success">Running</span>');
            $('#start-server').prop('disabled', true);
            $('#stop-server').prop('disabled', false);
        } else {
            $('#server-status').html('<span class="badge badge-danger">Stopped</span>');
            $('#start-server').prop('disabled', false);
            $('#stop-server').prop('disabled', true);
        }

        // Update port status
        if (status.port_open) {
            $('#port-status').html('Port ' + status.port + ': <span class="badge badge-success">Open</span>');
        } else {
            $('#port-status').html('Port ' + status.port + ': <span class="badge badge-warning">Closed</span>');
        }

        // Update PID
        $('#server-pid').text(status.pid || 'N/A');
        
        // Update last checked
        $('#last-checked').text(status.last_checked);
    }

    function showLoading() {
        $('#loadingModal').modal('show');
    }

    function hideLoading() {
        $('#loadingModal').modal('hide');
    }

    function showAlert(type, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;
        
        // Remove existing alerts
        $('.alert').remove();
        
        // Add new alert at the top of the card body
        $('.card-body').first().prepend(alertHtml);
        
        // Auto hide after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
});
</script>
@endsection
